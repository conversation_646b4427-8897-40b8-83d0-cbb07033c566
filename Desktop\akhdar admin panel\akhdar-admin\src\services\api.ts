import { collection, getDocs, doc, deleteDoc, updateDoc, query, where, getCountFromServer } from 'firebase/firestore'
import { db } from '../config/firebase.config'
import type { User } from '../types/user'

// Fetch dashboard counts from Firestore
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  try {
    const usersRef = collection(db, 'Users')

    // Get count of managers
    const managersQuery = query(usersRef, where('userType', '==', 'manager'))
    const managersSnapshot = await getCountFromServer(managersQuery)
    const managers = managersSnapshot.data().count

    // Get count of staff
    const staffQuery = query(usersRef, where('userType', '==', 'staff'))
    const staffSnapshot = await getCountFromServer(staffQuery)
    const staff = staffSnapshot.data().count

    // Get count of clients
    const clientsQuery = query(usersRef, where('userType', '==', 'client'))
    const clientsSnapshot = await getCountFromServer(clientsQuery)
    const clients = clientsSnapshot.data().count

    return {
      managers,
      staff,
      clients,
    }
  } catch (error) {
    console.error('Error fetching dashboard counts:', error)
    // Return fallback data if Firebase fails
    return {
      managers: 0,
      staff: 0,
      clients: 0,
    }
  }
}

// Fetch all users from Firestore
export async function fetchUsers(): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const querySnapshot = await getDocs(usersRef)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      // Only include users that are not admins
      if (userData.userType !== 'admin') {
        users.push({
          id: doc.id,
          name: userData.name || 'Unknown',
          email: userData.email || '',
          userType: userData.userType || 'client',
          createdAt: userData.createdAt || new Date().toISOString(),
          updatedAt: userData.updatedAt,
        })
      }
    })

    return users
  } catch (error) {
    console.error('Error fetching users:', error)
    throw new Error('Failed to fetch users from database')
  }
}

// Delete a user from Firestore
export async function deleteUser(userId: string): Promise<void> {
  try {
    const userRef = doc(db, 'Users', userId)
    await deleteDoc(userRef)
  } catch (error) {
    console.error('Error deleting user:', error)
    throw new Error('Failed to delete user from database')
  }
}

// Update a user in Firestore
export async function updateUser(userId: string, userData: Partial<User>): Promise<User> {
  try {
    const userRef = doc(db, 'Users', userId)
    const updateData = {
      ...userData,
      updatedAt: new Date().toISOString(),
    }

    // Remove id from update data if present
    const { id, ...dataToUpdate } = updateData as any

    await updateDoc(userRef, dataToUpdate)

    // Return the updated user data
    return {
      id: userId,
      ...userData,
      updatedAt: updateData.updatedAt,
    } as User
  } catch (error) {
    console.error('Error updating user:', error)
    throw new Error('Failed to update user in database')
  }
}

// Fetch users by type
export async function fetchUsersByType(userType: 'manager' | 'staff' | 'client'): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const q = query(usersRef, where('userType', '==', userType))
    const querySnapshot = await getDocs(q)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      users.push({
        id: doc.id,
        name: userData.name || 'Unknown',
        email: userData.email || '',
        userType: userData.userType || userType,
        createdAt: userData.createdAt || new Date().toISOString(),
        updatedAt: userData.updatedAt,
      })
    })

    return users
  } catch (error) {
    console.error(`Error fetching ${userType}s:`, error)
    throw new Error(`Failed to fetch ${userType}s from database`)
  }
}
